<template>
    <div class="approval-process-preset">
        <n-search-table-page
            ref="searchTablePageRef"
            :dataApi="api.sass.api.v1.workflow.templatePreset.list"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 1400,
                maxHeight: 'calc(100vh - 390px)'
            }"
            :search-props="{
                addText: '新增',
                showAdd: store.permissions.indexOf('approvalProcessPresetAdd') > -1,
                searchInputPlaceholder: '请输入唯一识别码 / 流程名称',
                inputWidth: 300
            }"
            :search-table-space="{
                size: 20
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            @add="handleEdit(null, '新增')"
            @reset="init"
        >
            <template #prefix="{ itemCount }"> 共{{ itemCount }}项 </template>
            <template #table_kind="{ row }">
                <n-tag
                    :type="kindOptions.find((item) => item.value === row.kind)?.type"
                    :bordered="false"
                    size="small"
                    round
                >
                    {{ kindOptions.find((item) => item.value === row.kind)?.label }}
                </n-tag>
            </template>
            <template #table_createdAt="{ row }">
                <n-time :time="row.createdAt" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_updatedAt="{ row }">
                <n-time :time="row.updatedAt" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center">
                    <n-permission has="approvalProcessPresetEdit">
                        <n-button @click="handleEdit(row, '编辑')" size="tiny" type="success"> 编辑 </n-button>
                    </n-permission>
                    <n-permission has="approvalProcessPresetDispense">
                        <n-button @click="bind(row)" size="tiny" type="primary"> 分发 </n-button>
                    </n-permission>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>
<script lang="ts" setup>
import { SaveForm } from '@/api/sass/api/v1/workflow/template-preset';
import useStore from '@/store/modules/main';
import { NButton } from 'naive-ui';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';

const store = useStore();

const columns = ref<TableColumns>([
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 60,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    {
        title: '唯一识别码',
        key: 'businessId',
        align: 'center',
        fixed: 'left',
        minWidth: 300,
        ellipsis: {
            tooltip: true
        },
        resizable: true
    },
    { title: '流程名称', key: 'name', align: 'center', minWidth: 200, ellipsis: { tooltip: true }, resizable: true },
    { title: '使用范围', key: 'kind', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { title: '更新人', key: 'updatedBy', align: 'center', width: 180, ellipsis: { tooltip: true } },
    { title: '更新时间', key: 'updatedAt', align: 'center', width: 180 },
    { title: '创建人', key: 'createdBy', align: 'center', width: 180, ellipsis: { tooltip: true } },
    { title: '创建时间', key: 'createdAt', align: 'center', width: 180 },
    {
        title: '操作',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 120
    }
]);

const kindOptions: { label: string; value: string; type: 'info' | 'success' }[] = [
    { label: '子公司', value: 'department', type: 'info' },
    { label: '集团', value: 'group', type: 'success' }
];
const handleEdit = async (row: SaveForm | null, type: string) => {
    $alert.dialog({
        title: type,
        content: import('./model/preset-form.vue'),
        width: '600px',
        props: {
            row,
            type,
            options: kindOptions,
            onSave: () => init()
        }
    });
};

const bind = async (row: any) => {
    $alert.dialog({
        title: '分发',
        content: import('./model/preset-bind.vue'),
        width: '600px',
        props: {
            id: row.id
        }
    });
};

const init = async () => {
    nextTick(() => searchTablePageRef.value?.initData());
};

const searchTablePageRef = ref();
</script>
